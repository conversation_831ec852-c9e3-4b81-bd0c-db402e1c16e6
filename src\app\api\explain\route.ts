import { NextRequest, NextResponse } from 'next/server';
import { getTextExplanation, ExplanationRequest } from '@/lib/openai';

export async function POST(request: NextRequest) {
  try {
    const body: ExplanationRequest = await request.json();
    
    // Validate the request
    if (!body.selectedText || !body.context) {
      return NextResponse.json(
        { error: 'Missing required fields: selectedText and context' },
        { status: 400 }
      );
    }

    // Ensure minimum text length for meaningful explanation
    if (body.selectedText.trim().length < 10) {
      return NextResponse.json(
        { error: 'Please select more text for a meaningful explanation (at least 10 characters)' },
        { status: 400 }
      );
    }

    const explanation = await getTextExplanation(body);
    
    return NextResponse.json(explanation);
  } catch (error) {
    console.error('Error in explain API:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      );
    }
    
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
