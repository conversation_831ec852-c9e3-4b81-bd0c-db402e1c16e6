import { NextRequest, NextResponse } from 'next/server';
import { searchVersesByTopic } from '@/lib/openai';

export async function POST(request: NextRequest) {
  try {
    const { topic } = await request.json();
    
    // Validate the request
    if (!topic || typeof topic !== 'string') {
      return NextResponse.json(
        { error: 'Missing or invalid topic parameter' },
        { status: 400 }
      );
    }

    // Ensure minimum topic length
    if (topic.trim().length < 2) {
      return NextResponse.json(
        { error: 'Topic must be at least 2 characters long' },
        { status: 400 }
      );
    }

    const verses = await searchVersesByTopic(topic.trim());
    
    return NextResponse.json({ verses });
  } catch (error) {
    console.error('Error in search-topic API:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      );
    }
    
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
