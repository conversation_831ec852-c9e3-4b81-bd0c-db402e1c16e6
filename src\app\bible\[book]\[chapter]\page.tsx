// ✅ Force this page to be dynamically rendered on the server
export const dynamic = "force-dynamic";
import ChapterNavigator from '@/components/ChapterNavigator'

interface Verse {
  book_id: string;
  book_name: string;
  chapter: number;
  verse: number;
  text: string;
}

type Props = {
  params: {
    book: string;
    chapter: string;
  };
};

export async function generateMetadata({ params }: Props) {
  const book = decodeURIComponent(params.book);
  const chapter = decodeURIComponent(params.chapter);
  return {
    title: `${book} ${chapter} - Bible Companion`,
  };
}

export default async function ChapterPage({ params }: Props) {
  const book = decodeURIComponent(params.book);
  const chapter = decodeURIComponent(params.chapter);

  const res = await fetch(`https://bible-api.com/${book}+${chapter}`);
  const data = await res.json();

  if (!data?.verses) {
    return <p className="p-6">Chapter not found.</p>;
  }

  const verses: Verse[] = data.verses;

  return (
    <main className="p-6 max-w-3xl mx-auto">
      <h1 className="text-3xl font-bold mb-4 capitalize">
        {book} {chapter}
      </h1>
      <div className="space-y-3">
        {verses.map((verse) => (
          <p key={verse.verse}>
            <span className="text-gray-500 font-semibold">{verse.verse}</span>{" "}
            {verse.text}
          </p>
        ))}
      </div>
      <ChapterNavigator book={decodeURIComponent(book)} chapter={parseInt(chapter)} />
    </main>
  );
}
