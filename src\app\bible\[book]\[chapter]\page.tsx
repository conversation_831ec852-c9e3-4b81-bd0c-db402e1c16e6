// ✅ Force this page to be dynamically rendered on the server
export const dynamic = "force-dynamic";
import ChapterNavigator from '@/components/ChapterNavigator'
import VerseBlock from '@/components/VerseBlock'
import ChapterHeader from '@/components/ChapterHeader'

interface Verse {
  book_id: string;
  book_name: string;
  chapter: number;
  verse: number;
  text: string;
}

type Props = {
  params: {
    book: string;
    chapter: string;
  };
};

export async function generateMetadata({ params }: Props) {
  const book = decodeURIComponent(params.book);
  const chapter = decodeURIComponent(params.chapter);
  return {
    title: `${book} ${chapter} - Bible Companion`,
  };
}

export default async function ChapterPage({ params }: Props) {
  const book = decodeURIComponent(params.book);
  const chapter = decodeURIComponent(params.chapter);

  const res = await fetch(`https://bible-api.com/${book}+${chapter}`);
  const data = await res.json();

  if (!data?.verses) {
    return <p className="p-6">Chapter not found.</p>;
  }

  const verses: Verse[] = data.verses;

  return (
    <main className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-4xl mx-auto p-4 sm:p-6">
        {/* Header */}
        <ChapterHeader
          book={book}
          chapter={parseInt(chapter)}
        />

        {/* Verses */}
        <div className="card p-4 sm:p-6 lg:p-8 mb-6 sm:mb-8">
          <div className="space-y-4 sm:space-y-6">
            {verses.map((verse) => (
              <VerseBlock key={verse.verse} verse={verse} />
            ))}
          </div>
        </div>

        {/* Navigation */}
        <ChapterNavigator book={decodeURIComponent(book)} chapter={parseInt(chapter)} />
      </div>
    </main>
  );
}
