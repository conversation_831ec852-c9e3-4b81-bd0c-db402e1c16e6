// ✅ Force this page to be dynamically rendered on the server
export const dynamic = "force-dynamic";
import ChapterNavigator from '@/components/ChapterNavigator'
import VerseBlock from '@/components/VerseBlock'
import ChapterSelector from '@/components/ChapterSelector'
import ReadingPreferences from '@/components/ReadingPreferences'

interface Verse {
  book_id: string;
  book_name: string;
  chapter: number;
  verse: number;
  text: string;
}

type Props = {
  params: {
    book: string;
    chapter: string;
  };
};

export async function generateMetadata({ params }: Props) {
  const book = decodeURIComponent(params.book);
  const chapter = decodeURIComponent(params.chapter);
  return {
    title: `${book} ${chapter} - Bible Companion`,
  };
}

export default async function ChapterPage({ params }: Props) {
  const book = decodeURIComponent(params.book);
  const chapter = decodeURIComponent(params.chapter);

  const res = await fetch(`https://bible-api.com/${book}+${chapter}`);
  const data = await res.json();

  if (!data?.verses) {
    return <p className="p-6">Chapter not found.</p>;
  }

  const verses: Verse[] = data.verses;

  return (
    <main className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-4xl mx-auto p-4 sm:p-6">
        {/* Header */}
        <div className="mb-6 sm:mb-8">
          <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-2">
            <button
              onClick={() => window.history.back()}
              className="hover:text-gray-700 dark:hover:text-gray-300 mr-2"
            >
              ← Back
            </button>
            <span>Bible</span>
            <span className="mx-2">•</span>
            <span className="capitalize">{book.replace('%20', ' ')}</span>
          </div>

          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
            <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white capitalize">
              {book.replace('%20', ' ')} {chapter}
            </h1>

            <div className="flex items-center gap-3">
              <ChapterSelector
                currentBook={book}
                currentChapter={parseInt(chapter)}
              />
              <ReadingPreferences />
            </div>
          </div>

          <p className="text-gray-600 dark:text-gray-400">
            Select any text to get AI-powered explanations and insights
          </p>
        </div>

        {/* Verses */}
        <div className="card p-4 sm:p-6 lg:p-8 mb-6 sm:mb-8">
          <div className="space-y-4 sm:space-y-6">
            {verses.map((verse) => (
              <VerseBlock key={verse.verse} verse={verse} />
            ))}
          </div>
        </div>

        {/* Navigation */}
        <ChapterNavigator book={decodeURIComponent(book)} chapter={parseInt(chapter)} />
      </div>
    </main>
  );
}
