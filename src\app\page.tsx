import BookSelector from '@/components/BookSelector'
import SearchBar from '@/components/SearchBar'
import TopicSearch from '@/components/TopicSearch'

export default function HomePage() {
  return (
    <main className="p-6 max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Bible Companion
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Read, explore, and understand Scripture with AI-powered insights
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <div className="space-y-6">
          <BookSelector />
          <SearchBar />
        </div>

        <div>
          <TopicSearch />
        </div>
      </div>

      <div className="mt-8 p-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          How to use AI explanations
        </h2>
        <p className="text-gray-700 dark:text-gray-300 text-sm">
          When reading any chapter, simply highlight any text (at least 10 characters) to get an AI-powered explanation with historical context, key themes, and practical applications.
        </p>
      </div>
    </main>
  )
}