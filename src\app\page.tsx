import BookSelector from '@/components/BookSelector'
import SearchBar from '@/components/SearchBar'
import TopicSearch from '@/components/TopicSearch'
import BookGrid from '@/components/BookGrid'

export default function HomePage() {
  return (
    <main className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-6xl mx-auto p-6">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
            Bible Companion
          </h1>
          <p className="text-gray-600 dark:text-gray-400 text-lg">
            Read, explore, and understand Scripture with AI-powered insights
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 xl:grid-cols-3">
          <div className="space-y-6">
            <BookSelector />
            <SearchBar />
          </div>

          <div className="md:col-span-1">
            <TopicSearch />
          </div>

          <div className="md:col-span-2 xl:col-span-1">
            <div className="card p-6 bg-blue-50 dark:bg-blue-900/20">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                How to use AI explanations
              </h2>
              <p className="text-gray-700 dark:text-gray-300 text-sm">
                When reading any chapter, simply highlight any text (at least 10 characters) to get an AI-powered explanation with historical context, key themes, and practical applications.
              </p>
            </div>
          </div>
        </div>

        <div className="mt-8">
          <BookGrid />
        </div>
      </div>
    </main>
  )
}