'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { books } from '@/data/books'

interface BookGridProps {
  className?: string;
}

export default function BookGrid({ className = '' }: BookGridProps) {
  const [selectedTestament, setSelectedTestament] = useState<'old' | 'new' | 'all'>('all')
  const router = useRouter()

  // Split books into Old and New Testament
  const oldTestament = books.slice(0, 39) // First 39 books
  const newTestament = books.slice(39) // Remaining books

  const filteredBooks = selectedTestament === 'old' 
    ? oldTestament 
    : selectedTestament === 'new' 
    ? newTestament 
    : books

  const handleBookClick = (bookSlug: string) => {
    router.push(`/bible/${bookSlug}/1`)
  }

  return (
    <div className={`card p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          Browse Books
        </h2>
        
        {/* Testament Filter */}
        <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
          <button
            onClick={() => setSelectedTestament('all')}
            className={`px-3 py-1 text-sm rounded-md transition-colors ${
              selectedTestament === 'all'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            All
          </button>
          <button
            onClick={() => setSelectedTestament('old')}
            className={`px-3 py-1 text-sm rounded-md transition-colors ${
              selectedTestament === 'old'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            Old Testament
          </button>
          <button
            onClick={() => setSelectedTestament('new')}
            className={`px-3 py-1 text-sm rounded-md transition-colors ${
              selectedTestament === 'new'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            New Testament
          </button>
        </div>
      </div>

      {/* Books Grid */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3">
        {filteredBooks.map((book) => (
          <button
            key={book.slug}
            onClick={() => handleBookClick(book.slug)}
            className="p-3 text-left bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors group"
          >
            <div className="font-medium text-gray-900 dark:text-white text-sm group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
              {book.name}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {book.chapters} chapters
            </div>
          </button>
        ))}
      </div>

      <div className="mt-4 text-xs text-gray-500 dark:text-gray-400 text-center">
        {filteredBooks.length} books • Click any book to start reading from chapter 1
      </div>
    </div>
  )
}
