'use client'

import { useRouter } from 'next/navigation'
import { books } from '@/data/books'

export default function BookSelector() {
  const router = useRouter()

  const handleSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const [book, chapter] = e.target.value.split(':')
    router.push(`/bible/${book}/${chapter}`)
  }

  return (
    <div className="card p-6">
      <div className="flex items-center mb-4">
        <svg className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
        </svg>
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
          Jump to Chapter
        </h2>
      </div>

      <select
        onChange={handleSelect}
        className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm"
        defaultValue=""
      >
        <option disabled value="">Select a book and chapter...</option>
        {books.map((book) =>
          Array.from({ length: book.chapters }, (_, i) => (
            <option key={`${book.slug}-${i + 1}`} value={`${book.slug}:${i + 1}`}>
              {book.name} {i + 1}
            </option>
          ))
        )}
      </select>

      <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
        Choose any book and chapter to start reading
      </p>
    </div>
  )
}
