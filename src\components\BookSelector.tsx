'use client'

import { useRouter } from 'next/navigation'
import { books } from '@/data/books'

export default function BookSelector() {
  const router = useRouter()

  const handleSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const [book, chapter] = e.target.value.split(':')
    router.push(`/bible/${book}/${chapter}`)
  }

  return (
    <div className="mb-6">
      <label className="block text-sm font-semibold mb-2">Jump to Book & Chapter</label>
      <select
        onChange={handleSelect}
        className="p-2 border rounded w-full max-w-sm"
        defaultValue=""
      >
        <option disabled value="">Select...</option>
        {books.map((book) =>
          Array.from({ length: book.chapters }, (_, i) => (
            <option key={`${book.slug}-${i + 1}`} value={`${book.slug}:${i + 1}`}>
              {book.name} {i + 1}
            </option>
          ))
        )}
      </select>
    </div>
  )
}
