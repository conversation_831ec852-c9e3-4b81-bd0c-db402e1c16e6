'use client'

import ChapterSelector from './ChapterSelector'
import ReadingPreferences from './ReadingPreferences'

interface ChapterHeaderProps {
  book: string;
  chapter: number;
}

export default function ChapterHeader({ book, chapter }: ChapterHeaderProps) {
  const handleBackClick = () => {
    if (typeof window !== 'undefined') {
      window.history.back()
    }
  }

  return (
    <div className="mb-6 sm:mb-8">
      <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-2">
        <button 
          onClick={handleBackClick}
          className="hover:text-gray-700 dark:hover:text-gray-300 mr-2"
        >
          ← Back
        </button>
        <span>Bible</span>
        <span className="mx-2">•</span>
        <span className="capitalize">{book.replace('%20', ' ')}</span>
      </div>
      
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
        <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white capitalize">
          {book.replace('%20', ' ')} {chapter}
        </h1>
        
        <div className="flex items-center gap-3">
          <ChapterSelector 
            currentBook={book} 
            currentChapter={chapter} 
          />
          <ReadingPreferences />
        </div>
      </div>
      
      <p className="text-gray-600 dark:text-gray-400">
        Select any text to get AI-powered explanations and insights
      </p>
    </div>
  )
}
