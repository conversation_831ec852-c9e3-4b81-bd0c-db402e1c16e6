'use client'

import { useRouter } from 'next/navigation'
import { getPrevNext } from '@/lib/navigate'

export default function ChapterNavigator({
  book,
  chapter,
}: {
  book: string
  chapter: number
}) {
  const router = useRouter()
  const { prev, next } = getPrevNext(book, chapter)

  return (
    <div className="flex justify-between mt-8 text-sm text-blue-600">
      {prev ? (
        <button
          onClick={() => router.push(`/bible/${prev.book}/${prev.chapter}`)}
          className="hover:underline"
        >
          ← {prev.book.replace('%20', ' ')} {prev.chapter}
        </button>
      ) : (
        <span />
      )}
      {next ? (
        <button
          onClick={() => router.push(`/bible/${next.book}/${next.chapter}`)}
          className="hover:underline"
        >
          {next.book.replace('%20', ' ')} {next.chapter} →
        </button>
      ) : (
        <span />
      )}
    </div>
  )
}
