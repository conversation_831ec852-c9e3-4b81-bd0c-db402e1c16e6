'use client'

import { useRouter } from 'next/navigation'
import { getPrevNext } from '@/lib/navigate'

export default function ChapterNavigator({
  book,
  chapter,
}: {
  book: string
  chapter: number
}) {
  const router = useRouter()
  const { prev, next } = getPrevNext(book, chapter)

  return (
    <div className="flex justify-between items-center mt-12 pt-8 border-t border-gray-200 dark:border-gray-700">
      {prev ? (
        <button
          onClick={() => router.push(`/bible/${prev.book}/${prev.chapter}`)}
          className="flex items-center px-4 py-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors group"
        >
          <svg className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          <div className="text-left">
            <div className="text-xs text-gray-500 dark:text-gray-400">Previous</div>
            <div className="font-medium">
              {prev.book.replace('%20', ' ')} {prev.chapter}
            </div>
          </div>
        </button>
      ) : (
        <div />
      )}

      <div className="text-center">
        <button
          onClick={() => router.push('/')}
          className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 transition-colors"
        >
          <svg className="w-5 h-5 mx-auto mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
          <div className="text-xs">Home</div>
        </button>
      </div>

      {next ? (
        <button
          onClick={() => router.push(`/bible/${next.book}/${next.chapter}`)}
          className="flex items-center px-4 py-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors group"
        >
          <div className="text-right">
            <div className="text-xs text-gray-500 dark:text-gray-400">Next</div>
            <div className="font-medium">
              {next.book.replace('%20', ' ')} {next.chapter}
            </div>
          </div>
          <svg className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      ) : (
        <div />
      )}
    </div>
  )
}
