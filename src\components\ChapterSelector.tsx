'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { books } from '@/data/books'

interface ChapterSelectorProps {
  currentBook: string;
  currentChapter: number;
  className?: string;
}

export default function ChapterSelector({ currentBook, currentChapter, className = '' }: ChapterSelectorProps) {
  const [isOpen, setIsOpen] = useState(false)
  const router = useRouter()

  // Find the current book data
  const bookData = books.find(book => book.slug === currentBook)
  
  if (!bookData) {
    return null
  }

  const handleChapterClick = (chapter: number) => {
    router.push(`/bible/${currentBook}/${chapter}`)
    setIsOpen(false)
  }

  // Generate chapter numbers array
  const chapters = Array.from({ length: bookData.chapters }, (_, i) => i + 1)

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center px-4 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
      >
        <span className="text-sm font-medium text-gray-900 dark:text-white">
          Chapter {currentChapter}
        </span>
        <svg 
          className={`w-4 h-4 ml-2 transition-transform ${isOpen ? 'rotate-180' : ''}`} 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute top-full left-0 mt-1 w-80 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg z-20 max-h-80 overflow-y-auto">
            <div className="p-3 border-b border-gray-200 dark:border-gray-600">
              <h3 className="font-medium text-gray-900 dark:text-white">
                {bookData.name} - Select Chapter
              </h3>
            </div>
            
            <div className="p-3">
              <div className="grid grid-cols-6 gap-2">
                {chapters.map((chapter) => (
                  <button
                    key={chapter}
                    onClick={() => handleChapterClick(chapter)}
                    className={`p-2 text-sm rounded-md transition-colors ${
                      chapter === currentChapter
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-white hover:bg-gray-200 dark:hover:bg-gray-500'
                    }`}
                  >
                    {chapter}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
