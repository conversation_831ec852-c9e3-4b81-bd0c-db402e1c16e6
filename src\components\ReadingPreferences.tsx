'use client'

import { useState, useEffect } from 'react'

interface ReadingPreferencesProps {
  className?: string;
}

export default function ReadingPreferences({ className = '' }: ReadingPreferencesProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [fontSize, setFontSize] = useState('medium')
  const [theme, setTheme] = useState('system')

  // Load preferences from localStorage on mount
  useEffect(() => {
    const savedFontSize = localStorage.getItem('bible-font-size') || 'medium'
    const savedTheme = localStorage.getItem('bible-theme') || 'system'
    
    setFontSize(savedFontSize)
    setTheme(savedTheme)
    
    // Apply font size
    applyFontSize(savedFontSize)
    
    // Apply theme
    applyTheme(savedTheme)
  }, [])

  const applyFontSize = (size: string) => {
    const root = document.documentElement
    switch (size) {
      case 'small':
        root.style.setProperty('--verse-font-size', '1rem')
        root.style.setProperty('--verse-line-height', '1.6')
        break
      case 'large':
        root.style.setProperty('--verse-font-size', '1.25rem')
        root.style.setProperty('--verse-line-height', '1.8')
        break
      case 'extra-large':
        root.style.setProperty('--verse-font-size', '1.5rem')
        root.style.setProperty('--verse-line-height', '2')
        break
      default: // medium
        root.style.setProperty('--verse-font-size', '1.1rem')
        root.style.setProperty('--verse-line-height', '1.8')
    }
  }

  const applyTheme = (newTheme: string) => {
    const root = document.documentElement
    
    if (newTheme === 'dark') {
      root.classList.add('dark')
    } else if (newTheme === 'light') {
      root.classList.remove('dark')
    } else {
      // System theme
      if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
        root.classList.add('dark')
      } else {
        root.classList.remove('dark')
      }
    }
  }

  const handleFontSizeChange = (newSize: string) => {
    setFontSize(newSize)
    localStorage.setItem('bible-font-size', newSize)
    applyFontSize(newSize)
  }

  const handleThemeChange = (newTheme: string) => {
    setTheme(newTheme)
    localStorage.setItem('bible-theme', newTheme)
    applyTheme(newTheme)
  }

  const fontSizeOptions = [
    { value: 'small', label: 'Small', description: 'Compact reading' },
    { value: 'medium', label: 'Medium', description: 'Default size' },
    { value: 'large', label: 'Large', description: 'Comfortable reading' },
    { value: 'extra-large', label: 'Extra Large', description: 'Maximum readability' },
  ]

  const themeOptions = [
    { value: 'system', label: 'System', description: 'Follow device setting' },
    { value: 'light', label: 'Light', description: 'Light theme' },
    { value: 'dark', label: 'Dark', description: 'Dark theme' },
  ]

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center px-3 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 transition-colors"
        title="Reading preferences"
      >
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
        </svg>
        <span className="ml-2 text-sm hidden sm:inline">Preferences</span>
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute top-full right-0 mt-1 w-80 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg z-20">
            <div className="p-4">
              <h3 className="font-medium text-gray-900 dark:text-white mb-4">
                Reading Preferences
              </h3>
              
              {/* Font Size */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Font Size
                </label>
                <div className="space-y-2">
                  {fontSizeOptions.map((option) => (
                    <button
                      key={option.value}
                      onClick={() => handleFontSizeChange(option.value)}
                      className={`w-full text-left p-2 rounded-md transition-colors ${
                        fontSize === option.value
                          ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-900 dark:text-blue-300'
                          : 'hover:bg-gray-100 dark:hover:bg-gray-600'
                      }`}
                    >
                      <div className="font-medium text-sm">{option.label}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {option.description}
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Theme */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Theme
                </label>
                <div className="space-y-2">
                  {themeOptions.map((option) => (
                    <button
                      key={option.value}
                      onClick={() => handleThemeChange(option.value)}
                      className={`w-full text-left p-2 rounded-md transition-colors ${
                        theme === option.value
                          ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-900 dark:text-blue-300'
                          : 'hover:bg-gray-100 dark:hover:bg-gray-600'
                      }`}
                    >
                      <div className="font-medium text-sm">{option.label}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {option.description}
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
