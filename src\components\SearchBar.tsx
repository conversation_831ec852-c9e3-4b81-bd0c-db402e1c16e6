'use client'

import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { books } from '@/data/books'

export default function SearchBar() {
  const [query, setQuery] = useState('')
  const router = useRouter()

  const matches = books.filter((book) =>
    book.name.toLowerCase().includes(query.toLowerCase())
  )

  const handleSelect = (bookSlug: string) => {
    router.push(`/bible/${bookSlug}/1`)
    setQuery('')
  }

  return (
    <div className="mb-6">
      <label className="block text-sm font-semibold mb-1">Search Books</label>
      <input
        type="text"
        placeholder="e.g. Romans, Psalms, 1 John"
        className="p-2 border rounded w-full max-w-sm"
        value={query}
        onChange={(e) => setQuery(e.target.value)}
      />
      {query && (
        <ul className="mt-2 bg-white border rounded max-w-sm shadow">
          {matches.map((book) => (
            <li
              key={book.slug}
              onClick={() => handleSelect(book.slug)}
              className="p-2 hover:bg-gray-100 cursor-pointer"
            >
              {book.name}
            </li>
          ))}
          {matches.length === 0 && (
            <li className="p-2 text-gray-500">No matches found</li>
          )}
        </ul>
      )}
    </div>
  )
}
