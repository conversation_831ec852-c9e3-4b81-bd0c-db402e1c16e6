'use client'

import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { books } from '@/data/books'

export default function SearchBar() {
  const [query, setQuery] = useState('')
  const router = useRouter()

  const matches = books.filter((book) =>
    book.name.toLowerCase().includes(query.toLowerCase())
  )

  const handleSelect = (bookSlug: string) => {
    router.push(`/bible/${bookSlug}/1`)
    setQuery('')
  }

  return (
    <div className="card p-6">
      <div className="flex items-center mb-4">
        <svg className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
          Search Books
        </h2>
      </div>

      <div className="relative">
        <input
          type="text"
          placeholder="e.g. Romans, Psalms, 1 John..."
          className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
        />

        {query && (
          <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto">
            {matches.map((book) => (
              <button
                key={book.slug}
                onClick={() => handleSelect(book.slug)}
                className="w-full text-left p-3 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors first:rounded-t-lg last:rounded-b-lg"
              >
                <span className="font-medium text-gray-900 dark:text-white">
                  {book.name}
                </span>
                <span className="text-sm text-gray-500 dark:text-gray-400 ml-2">
                  {book.chapters} chapters
                </span>
              </button>
            ))}
            {matches.length === 0 && (
              <div className="p-3 text-gray-500 dark:text-gray-400 text-center">
                No books found matching "{query}"
              </div>
            )}
          </div>
        )}
      </div>

      <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
        Start typing to search for Bible books
      </p>
    </div>
  )
}
