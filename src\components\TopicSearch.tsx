'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { parseVerseReference, bookNameToSlug } from '@/utils/embeddings'

interface TopicSearchProps {
  className?: string;
}

interface SearchResult {
  verses: string[];
  loading: boolean;
  error: string | null;
}

export default function TopicSearch({ className = '' }: TopicSearchProps) {
  const [query, setQuery] = useState('')
  const [result, setResult] = useState<SearchResult>({
    verses: [],
    loading: false,
    error: null,
  })
  const router = useRouter()

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!query.trim()) {
      return
    }

    setResult({
      verses: [],
      loading: true,
      error: null,
    })

    try {
      const response = await fetch('/api/search-topic', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ topic: query.trim() }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to search verses')
      }

      const data = await response.json()
      
      setResult({
        verses: data.verses || [],
        loading: false,
        error: null,
      })

    } catch (error) {
      console.error('Error searching verses:', error)
      setResult({
        verses: [],
        loading: false,
        error: error instanceof Error ? error.message : 'An error occurred',
      })
    }
  }

  const handleVerseClick = (verseReference: string) => {
    const parsed = parseVerseReference(verseReference)
    if (parsed) {
      const bookSlug = bookNameToSlug(parsed.book)
      router.push(`/bible/${bookSlug}/${parsed.chapter}`)
    }
  }

  const clearResults = () => {
    setResult({
      verses: [],
      loading: false,
      error: null,
    })
    setQuery('')
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
      <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
        Search Verses by Topic
      </h2>
      
      <form onSubmit={handleSearch} className="mb-4">
        <div className="flex gap-3">
          <input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="e.g., love, forgiveness, strength, hope..."
            className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            disabled={result.loading}
          />
          <button
            type="submit"
            disabled={result.loading || !query.trim()}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {result.loading ? 'Searching...' : 'Search'}
          </button>
        </div>
      </form>

      {result.loading && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600 dark:text-gray-400">Finding relevant verses...</span>
        </div>
      )}

      {result.error && (
        <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg mb-4">
          <p className="text-red-700 dark:text-red-400">{result.error}</p>
        </div>
      )}

      {result.verses.length > 0 && (
        <div>
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-medium text-gray-900 dark:text-white">
              Verses about "{query}" ({result.verses.length} found)
            </h3>
            <button
              onClick={clearResults}
              className="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            >
              Clear
            </button>
          </div>
          
          <div className="space-y-2">
            {result.verses.map((verse, index) => (
              <button
                key={index}
                onClick={() => handleVerseClick(verse)}
                className="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors"
              >
                <span className="font-medium text-blue-600 dark:text-blue-400">
                  {verse}
                </span>
              </button>
            ))}
          </div>
          
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-3">
            Click on any verse reference to read it in context
          </p>
        </div>
      )}

      {!result.loading && !result.error && result.verses.length === 0 && query && (
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          <p>No verses found for "{query}". Try a different topic or keyword.</p>
        </div>
      )}
    </div>
  )
}
