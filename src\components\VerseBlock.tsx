'use client'

import { useState, useRef } from 'react'
import { getSelectedText, clearSelection, getSurroundingContext } from '@/utils/embeddings'
import { ExplanationResponse } from '@/lib/openai'

interface Verse {
  book_id: string;
  book_name: string;
  chapter: number;
  verse: number;
  text: string;
}

interface VerseBlockProps {
  verse: Verse;
}

interface ExplanationModal {
  isOpen: boolean;
  selectedText: string;
  explanation: ExplanationResponse | null;
  loading: boolean;
  error: string | null;
}

export default function VerseBlock({ verse }: VerseBlockProps) {
  const [modal, setModal] = useState<ExplanationModal>({
    isOpen: false,
    selectedText: '',
    explanation: null,
    loading: false,
    error: null,
  });

  const verseRef = useRef<HTMLParagraphElement>(null);

  const handleTextSelection = async () => {
    const selection = getSelectedText();

    if (!selection || selection.text.length < 10) {
      return;
    }

    // Get the full verse text for context
    const fullVerseText = verse.text;
    const surroundingContext = getSurroundingContext(selection.text, fullVerseText);

    setModal({
      isOpen: true,
      selectedText: selection.text,
      explanation: null,
      loading: true,
      error: null,
    });

    try {
      const response = await fetch('/api/explain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          selectedText: selection.text,
          context: {
            book: verse.book_name,
            chapter: verse.chapter,
            verse: verse.verse,
            surroundingText: surroundingContext,
          },
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to get explanation');
      }

      const explanation: ExplanationResponse = await response.json();

      setModal(prev => ({
        ...prev,
        explanation,
        loading: false,
      }));

    } catch (error) {
      console.error('Error getting explanation:', error);
      setModal(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'An error occurred',
      }));
    }
  };

  const closeModal = () => {
    setModal({
      isOpen: false,
      selectedText: '',
      explanation: null,
      loading: false,
      error: null,
    });
    clearSelection();
  };

  return (
    <>
      <p
        ref={verseRef}
        className="verse-text leading-relaxed text-gray-800 dark:text-gray-200 cursor-text select-text"
        onMouseUp={handleTextSelection}
      >
        <span className="text-blue-600 dark:text-blue-400 font-semibold mr-2 text-sm">
          {verse.verse}
        </span>
        {verse.text}
      </p>

      {/* Explanation Modal */}
      {modal.isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-end sm:items-center justify-center p-0 sm:p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-t-lg sm:rounded-lg max-w-2xl w-full max-h-[90vh] sm:max-h-[80vh] overflow-y-auto">
            <div className="p-4 sm:p-6">
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Text Explanation
                </h3>
                <button
                  onClick={closeModal}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Selected text:</p>
                <p className="font-medium text-gray-900 dark:text-white">"{modal.selectedText}"</p>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {verse.book_name} {verse.chapter}:{verse.verse}
                </p>
              </div>

              {modal.loading && (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <span className="ml-3 text-gray-600 dark:text-gray-400">Getting explanation...</span>
                </div>
              )}

              {modal.error && (
                <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                  <p className="text-red-700 dark:text-red-400">{modal.error}</p>
                </div>
              )}

              {modal.explanation && (
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Explanation</h4>
                    <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                      {modal.explanation.explanation}
                    </p>
                  </div>

                  {modal.explanation.keyThemes && modal.explanation.keyThemes.length > 0 && (
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Key Themes</h4>
                      <div className="flex flex-wrap gap-2">
                        {modal.explanation.keyThemes.map((theme, index) => (
                          <span
                            key={index}
                            className="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded-full text-sm"
                          >
                            {theme}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {modal.explanation.historicalContext && (
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Historical Context</h4>
                      <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                        {modal.explanation.historicalContext}
                      </p>
                    </div>
                  )}

                  {modal.explanation.practicalApplication && (
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Practical Application</h4>
                      <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                        {modal.explanation.practicalApplication}
                      </p>
                    </div>
                  )}
                </div>
              )}

              <div className="mt-6 flex justify-end">
                <button
                  onClick={closeModal}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}