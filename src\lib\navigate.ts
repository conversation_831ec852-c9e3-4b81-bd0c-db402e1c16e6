import { books } from '@/data/books'

export function getPrevNext(bookSlug: string, chapter: number) {
  const index = books.findIndex((b) => b.slug === bookSlug)

  if (index === -1) return { prev: null, next: null }

  const book = books[index]
  const prev =
    chapter > 1
      ? { book: book.slug, chapter: chapter - 1 }
      : index > 0
      ? { book: books[index - 1].slug, chapter: books[index - 1].chapters }
      : null

  const next =
    chapter < book.chapters
      ? { book: book.slug, chapter: chapter + 1 }
      : index < books.length - 1
      ? { book: books[index + 1].slug, chapter: 1 }
      : null

  return { prev, next }
}
