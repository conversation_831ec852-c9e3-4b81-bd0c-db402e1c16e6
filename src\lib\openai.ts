import OpenAI from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export interface ExplanationRequest {
  selectedText: string;
  context: {
    book: string;
    chapter: number;
    verse?: number;
    surroundingText?: string;
  };
}

export interface ExplanationResponse {
  explanation: string;
  keyThemes: string[];
  historicalContext?: string;
  practicalApplication?: string;
}

/**
 * Get AI explanation for selected Bible text
 */
export async function getTextExplanation(request: ExplanationRequest): Promise<ExplanationResponse> {
  try {
    const { selectedText, context } = request;

    const prompt = `
You are a knowledgeable Bible scholar and teacher. A user has selected the following text from ${context.book} ${context.chapter}${context.verse ? `:${context.verse}` : ''}:

Selected text: "${selectedText}"

${context.surroundingText ? `Context: ${context.surroundingText}` : ''}

Please provide:
1. A clear, accessible explanation of this text
2. Key themes or concepts
3. Historical context if relevant
4. Practical application for modern readers

Keep your response concise but insightful, suitable for both new and experienced Bible readers.

Respond in JSON format:
{
  "explanation": "Main explanation here",
  "keyThemes": ["theme1", "theme2", "theme3"],
  "historicalContext": "Historical background if relevant",
  "practicalApplication": "How this applies today"
}
`;

    const completion = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || 'gpt-4o-mini',
      messages: [
        {
          role: 'system',
          content: 'You are a helpful Bible scholar who provides clear, accurate, and accessible explanations of Biblical text. Always respond with valid JSON.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 800,
    });

    const responseText = completion.choices[0]?.message?.content;
    if (!responseText) {
      throw new Error('No response from OpenAI');
    }

    // Parse the JSON response
    const parsedResponse = JSON.parse(responseText);
    return parsedResponse as ExplanationResponse;

  } catch (error) {
    console.error('Error getting text explanation:', error);
    throw new Error('Failed to get explanation. Please try again.');
  }
}

/**
 * Search for verses by topic using semantic search
 */
export async function searchVersesByTopic(topic: string): Promise<string[]> {
  try {
    const prompt = `
You are a Bible search assistant. The user is looking for verses related to: "${topic}"

Please provide 5-8 specific Bible verse references (book chapter:verse format) that directly relate to this topic.
Focus on well-known, relevant verses that would be helpful for someone studying this topic.

Examples of good format:
- John 3:16
- Romans 8:28
- Philippians 4:13

Respond with just the verse references, one per line, no additional text.
`;

    const completion = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || 'gpt-4o-mini',
      messages: [
        {
          role: 'system',
          content: 'You are a helpful Bible search assistant. Provide only verse references in the requested format.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.3,
      max_tokens: 200,
    });

    const responseText = completion.choices[0]?.message?.content;
    if (!responseText) {
      throw new Error('No response from OpenAI');
    }

    // Parse the response into an array of verse references
    const verses = responseText
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0 && line.includes(':'))
      .map(line => line.replace(/^[-*]\s*/, '')); // Remove bullet points if present

    return verses;

  } catch (error) {
    console.error('Error searching verses by topic:', error);
    throw new Error('Failed to search verses. Please try again.');
  }
}

export default openai;