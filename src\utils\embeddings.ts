/**
 * Utility functions for text selection and highlighting
 */

export interface TextSelection {
  text: string;
  startOffset: number;
  endOffset: number;
  containerElement: Element;
}

/**
 * Get the currently selected text and its context
 */
export function getSelectedText(): TextSelection | null {
  const selection = window.getSelection();

  if (!selection || selection.rangeCount === 0) {
    return null;
  }

  const range = selection.getRangeAt(0);
  const selectedText = range.toString().trim();

  if (!selectedText) {
    return null;
  }

  // Find the container element (should be a verse paragraph)
  let container = range.commonAncestorContainer;
  if (container.nodeType === Node.TEXT_NODE) {
    container = container.parentElement!;
  }

  // Find the closest verse container
  while (container && !container.classList?.contains('verse-text')) {
    container = container.parentElement!;
  }

  if (!container) {
    return null;
  }

  return {
    text: selectedText,
    startOffset: range.startOffset,
    endOffset: range.endOffset,
    containerElement: container as Element,
  };
}

/**
 * Clear current text selection
 */
export function clearSelection(): void {
  const selection = window.getSelection();
  if (selection) {
    selection.removeAllRanges();
  }
}

/**
 * Get surrounding context for a selected text within a verse
 */
export function getSurroundingContext(
  selectedText: string,
  fullVerseText: string,
  contextWords: number = 10
): string {
  const selectedIndex = fullVerseText.indexOf(selectedText);
  if (selectedIndex === -1) {
    return fullVerseText;
  }

  const words = fullVerseText.split(' ');
  const selectedWords = selectedText.split(' ');

  // Find the position of selected text in words array
  let startWordIndex = -1;
  for (let i = 0; i <= words.length - selectedWords.length; i++) {
    const candidate = words.slice(i, i + selectedWords.length).join(' ');
    if (candidate === selectedText) {
      startWordIndex = i;
      break;
    }
  }

  if (startWordIndex === -1) {
    return fullVerseText;
  }

  const endWordIndex = startWordIndex + selectedWords.length - 1;

  // Get context words before and after
  const contextStart = Math.max(0, startWordIndex - contextWords);
  const contextEnd = Math.min(words.length - 1, endWordIndex + contextWords);

  const contextWords_before = words.slice(contextStart, startWordIndex);
  const contextWords_after = words.slice(endWordIndex + 1, contextEnd + 1);

  return [
    contextWords_before.join(' '),
    `**${selectedText}**`,
    contextWords_after.join(' ')
  ].filter(Boolean).join(' ');
}

/**
 * Parse verse reference string (e.g., "John 3:16") into components
 */
export function parseVerseReference(reference: string): {
  book: string;
  chapter: number;
  verse: number;
} | null {
  const match = reference.match(/^(.+?)\s+(\d+):(\d+)$/);
  if (!match) {
    return null;
  }

  return {
    book: match[1].trim(),
    chapter: parseInt(match[2], 10),
    verse: parseInt(match[3], 10),
  };
}

/**
 * Convert book name to URL slug format
 */
export function bookNameToSlug(bookName: string): string {
  return bookName
    .toLowerCase()
    .replace(/\s+/g, '%20');
}